# Default values for fanme-backend-payment.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1
hpa: false
maxReplicas: 20
targetCPUUtilization: 80
pdb: true
minAvailable: 1
image:
  fanme-backend-payment:
    repository: 638984414044.dkr.ecr.ap-northeast-1.amazonaws.com/fanme-backend
    tag: febfebc1ea98bc3e35f6f17378c9aeb7b5cfefa4
    pullPolicy: Always
nameOverride: ""
fullnameOverride: ""
ingress:
  enabled: false
  className: ""
  annotations: {}
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  path: /
  hosts:
    - chart-example.local
  tls: []
resources:
  limits:
    cpu: 1000m
    memory: 1024Mi
  requests:
    cpu: 20m
    memory: 32Mi
livenessProbe:
  path: /hc
  port: 8080
  initialDelaySeconds: 130
  timeoutSeconds: 10
  periodSeconds: 15
  failureThreshold: 5
readinessProbe:
  path: /hc
  port: 8080
  initialDelaySeconds: 120
  timeoutSeconds: 5
  periodSeconds: 10
  failureThreshold: 3
nodeSelector: {}
tolerations: []
affinity: {}
fanme-backend-payment:
  database:
    username: ""
    password: ""
    host: ""
    port: ""
    name: ""
env: ""
