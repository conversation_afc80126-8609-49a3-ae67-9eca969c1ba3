CREATE TABLE IF NOT EXISTS proxy_access_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    proxy_user_uid VARCHAR(255),  -- 実際の操作者のUID（代理で操作した人）。通常アクセスの場合はNULL
    target_user_uid VARCHAR(255) NOT NULL,  -- 代理対象のユーザーUID（代理アクセスの場合）または操作者のUID（通常アクセスの場合）
    method VARCHAR(10) NOT NULL CHECK (method IN ('POST', 'PUT', 'DELETE')),
    url TEXT NOT NULL,
    request JSON,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_proxy_access_logs_proxy_created (proxy_user_uid, created_at DESC),
    INDEX idx_proxy_access_logs_target_created (target_user_uid, created_at DESC),
    INDEX idx_proxy_access_logs_url (url(255))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_bin;