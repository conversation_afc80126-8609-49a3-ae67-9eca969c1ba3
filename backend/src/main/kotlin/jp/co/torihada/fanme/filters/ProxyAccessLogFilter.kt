package jp.co.torihada.fanme.filters

import com.fasterxml.jackson.databind.ObjectMapper
import io.smallrye.jwt.auth.principal.DefaultJWTCallerPrincipal
import jakarta.inject.Inject
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.container.ContainerRequestFilter
import jakarta.ws.rs.container.ContainerResponseContext
import jakarta.ws.rs.container.ContainerResponseFilter
import jakarta.ws.rs.ext.Provider
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import jp.co.torihada.fanme.modules.console.controllers.ProxyAccessLogController
import jp.co.torihada.fanme.modules.console.services.ProxyAccessTokenService
import jp.co.torihada.fanme.modules.console.usecases.CreateProxyAccessLog
import org.jboss.logging.Logger

@Provider
class ProxyAccessLogFilter : ContainerRequestFilter, ContainerResponseFilter {

    @Inject lateinit var logger: Logger

    @Inject lateinit var proxyTokenService: ProxyAccessTokenService

    @Inject lateinit var proxyAccessLogController: ProxyAccessLogController

    @Inject lateinit var objectMapper: ObjectMapper

    companion object {
        private const val PROXY_TOKEN_HEADER = "X-Proxy-Access-Token"
        private val LOGGED_METHODS = setOf("POST", "PUT", "DELETE")
        private const val REQUEST_BODY_PROPERTY = "proxy_access_request_body"
    }

    /** リクエスト受信時に実行される リクエストボディをキャッシュして後で使用できるようにする */
    override fun filter(requestContext: ContainerRequestContext) {
        try {
            val method = requestContext.method

            if (method in LOGGED_METHODS && requestContext.hasEntity()) {
                val inputStream = requestContext.entityStream
                val outputStream = ByteArrayOutputStream()

                inputStream.use { input ->
                    outputStream.use { output ->
                        val buffer = ByteArray(1024)
                        var bytesRead: Int
                        var totalSize = 0

                        while (input.read(buffer).also { bytesRead = it } != -1) {
                            output.write(buffer, 0, bytesRead)
                            totalSize += bytesRead
                        }
                    }
                }

                val bodyBytes = outputStream.toByteArray()
                val bodyString = String(bodyBytes, Charsets.UTF_8)

                requestContext.setProperty(REQUEST_BODY_PROPERTY, bodyString)

                requestContext.entityStream = ByteArrayInputStream(bodyBytes)
            }
        } catch (e: Exception) {
            logger.error("Failed to cache request body", e)
        }
    }

    override fun filter(
        requestContext: ContainerRequestContext,
        responseContext: ContainerResponseContext,
    ) {
        try {
            val method = requestContext.method
            val proxyToken = requestContext.headers[PROXY_TOKEN_HEADER]?.firstOrNull()

            // ログ対象のメソッドかつ成功時のみ記録
            val statusCode = responseContext.status
            if (method in LOGGED_METHODS && statusCode == 200) {
                logAccess(requestContext, proxyToken)
            }
        } catch (e: Exception) {
            // ログ記録の失敗がレスポンスに影響しないようにする
            logger.error("Failed to log access", e)
        }
    }

    private fun logAccess(requestContext: ContainerRequestContext, proxyToken: String?) {
        if (proxyToken != null) {
            logProxyAccess(requestContext, proxyToken)
        } else {
            logRegularAccess(requestContext)
        }
    }

    private fun logProxyAccess(requestContext: ContainerRequestContext, proxyToken: String) {
        proxyTokenService
            .retrieveProxyAccessData(proxyToken)
            .subscribe()
            .with(
                { proxyData ->
                    if (proxyData != null) {
                        createAccessLog(
                            requestContext = requestContext,
                            targetUserUid = proxyData.targetUserUid,
                            proxyUserUid = proxyData.proxyUserUid,
                        )
                    }
                },
                { error -> logger.error("Failed to retrieve proxy access data", error) },
            )
    }

    private fun logRegularAccess(requestContext: ContainerRequestContext) {
        try {
            val userUid = getUserUidFromJWT(requestContext)
            if (userUid != null) {
                createAccessLog(
                    requestContext = requestContext,
                    targetUserUid = userUid,
                    proxyUserUid = null,
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to create regular access log", e)
        }
    }

    private fun getUserUidFromJWT(requestContext: ContainerRequestContext): String? {
        val securityContext = requestContext.securityContext ?: return null
        val principal = securityContext.userPrincipal as? DefaultJWTCallerPrincipal ?: return null
        return principal.subject
    }

    private fun createAccessLog(
        requestContext: ContainerRequestContext,
        targetUserUid: String,
        proxyUserUid: String?,
    ) {
        try {
            val input =
                CreateProxyAccessLog.Input(
                    proxyUserUid = proxyUserUid,
                    targetUserUid = targetUserUid,
                    method = requestContext.method,
                    url = requestContext.uriInfo.absolutePath.toString(),
                    request = requestContext.getProperty(REQUEST_BODY_PROPERTY) as? String,
                )
            proxyAccessLogController.createProxyAccessLog(input)
        } catch (e: Exception) {
            logger.error("Failed to create access log", e)
        }
    }
}
