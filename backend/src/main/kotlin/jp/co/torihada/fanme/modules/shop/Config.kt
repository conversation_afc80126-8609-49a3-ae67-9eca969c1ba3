package jp.co.torihada.fanme.modules.shop

import io.quarkus.runtime.annotations.StaticInitSafe
import io.smallrye.config.ConfigMapping
import io.smallrye.config.WithDefault

@ConfigMapping(prefix = "config.shop")
@StaticInitSafe
interface Config {
    fun shopFrontUrl(): String

    fun shopPaymentUrl(): String

    fun s3Endpoint(): String

    fun s3BucketName(): String

    fun s3BucketNameForSmapo(): String

    fun cloudFrontDomain(): String

    fun cloudFrontKeyPairId(): String

    fun cloudFrontPrivateKey(): String

    fun smapoGcpBucketName(): String

    @WithDefault("false") fun featureTogglePrintGacha(): Boolean

    @WithDefault("false") fun featureToggleFreeShipping(): Boolean
}
