package jp.co.torihada.fanme.modules.console.services

import io.quarkus.redis.datasource.RedisDataSource
import io.quarkus.redis.datasource.hash.HashCommands
import io.smallrye.mutiny.Uni
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.security.SecureRandom
import java.time.Duration
import java.util.Base64
import org.jboss.logging.Logger

@ApplicationScoped
class ProxyAccessTokenService {
    @Inject private lateinit var redisDataSource: RedisDataSource

    @Inject private lateinit var logger: Logger

    companion object {
        private const val REDIS_KEY_PREFIX = "proxy:"
        private const val TOKEN_EXPIRATION_SECONDS = 120L
        private const val TOKEN_BYTE_SIZE = 32
    }

    data class ProxyAccessData(
        val proxyUserUid: String,
        val targetUserUid: String,
        val targetUserFanmeToken: String,
    )

    fun createProxyAccessToken(data: ProxyAccessData): String {
        val proxyAccessToken = generateRandomToken()
        val redisKey = "$REDIS_KEY_PREFIX$proxyAccessToken"

        val hashCommands: HashCommands<String, String, String> =
            redisDataSource.hash(String::class.java)

        val redisData =
            mapOf(
                "proxy_user_uid" to data.proxyUserUid,
                "target_user_uid" to data.targetUserUid,
                "target_user_fanme_token" to data.targetUserFanmeToken,
            )

        hashCommands.hset(redisKey, redisData)
        redisDataSource.key().expire(redisKey, Duration.ofSeconds(TOKEN_EXPIRATION_SECONDS))

        return proxyAccessToken
    }

    fun retrieveProxyAccessData(proxyAccessToken: String): Uni<ProxyAccessData?> {
        val redisKey = "$REDIS_KEY_PREFIX$proxyAccessToken"

        val hashCommands: HashCommands<String, String, String> =
            redisDataSource.hash(String::class.java)

        return Uni.createFrom()
            .item {
                val redisData = hashCommands.hgetall(redisKey)
                if (redisData.isNullOrEmpty()) {
                    return@item null
                }

                val targetUserFanmeToken = redisData["target_user_fanme_token"]
                val proxyUserUid = redisData["proxy_user_uid"]
                val targetUserUid = redisData["target_user_uid"]

                if (targetUserFanmeToken != null && proxyUserUid != null && targetUserUid != null) {
                    ProxyAccessData(
                        proxyUserUid = proxyUserUid,
                        targetUserUid = targetUserUid,
                        targetUserFanmeToken = targetUserFanmeToken,
                    )
                } else {
                    logger.warn("Incomplete proxy access token data for key: $redisKey")
                    null
                }
            }
            .onFailure()
            .invoke { e ->
                logger.error(
                    "Failed to retrieve proxy access token from Redis. Key: $redisKey, Error: ${e.message}",
                    e,
                )
            }
            .onFailure()
            .recoverWithNull()
    }

    private fun generateRandomToken(): String {
        val bytes = ByteArray(TOKEN_BYTE_SIZE)
        SecureRandom().nextBytes(bytes)
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes)
    }
}
