package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.console.usecases.CreateProxyAccessLog

@ApplicationScoped
class ProxyAccessLogController {
    @Inject lateinit var createProxyAccessLog: CreateProxyAccessLog

    @Transactional
    fun createProxyAccessLog(input: CreateProxyAccessLog.Input) {
        createProxyAccessLog.execute(input).getOrThrow()
    }
}
