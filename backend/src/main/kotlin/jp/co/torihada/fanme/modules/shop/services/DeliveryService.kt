package jp.co.torihada.fanme.modules.shop.services

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.shop.Config as ShopConfig
import jp.co.torihada.fanme.modules.shop.Const
import jp.co.torihada.fanme.modules.shop.Const.FREE_SHIPPING_MIN_AMOUNT_PHYSICAL_ITEM
import jp.co.torihada.fanme.modules.shop.Const.FREE_SHIPPING_MIN_AMOUNT_PRINT_ITEM
import jp.co.torihada.fanme.modules.shop.models.CartItem
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemType
import kotlin.collections.forEach

@ApplicationScoped
class DeliveryService {
    @Inject private lateinit var shopConfig: ShopConfig

    fun getDeliveryFeeByItemType(itemType: ItemType): Int? {
        return when (itemType) {
            ItemType.CHEKI -> Const.CHEKI_DELIVERY_FEE
            ItemType.PRINT_GACHA -> Const.PRINT_GACHA_DELIVERY_FEE
            else -> null
        }
    }

    fun getCartItemsDeliveryFee(cartItems: List<CartItem>): Int? {
        val deliveryFee = getBaseDeliveryFee(cartItems.map { it.item })
        var printItemAmount = 0
        var chekiItemAmount = 0

        cartItems.forEach { cartItem ->
            val item = cartItem.item
            val singleFile = cartItem.singleFile
            if (item.itemType == ItemType.REAL_PHOTO) {
                printItemAmount += singleFile?.price ?: (item.price * cartItem.quantity)
            }
            if (item.itemType == ItemType.CHEKI) {
                chekiItemAmount += singleFile?.price ?: (item.price * cartItem.quantity)
            }
        }
        return if (isFreeShipping(printItemAmount, chekiItemAmount)) 0 else deliveryFee
    }

    fun getSingleOrderDeliveryFee(amount: Int, item: Item): Int? {
        val deliveryFee = getBaseDeliveryFee(listOf(item))
        return if (isFreeShipping(amount, 0)) 0 else deliveryFee
    }

    private fun getBaseDeliveryFee(items: List<Item>): Int? {
        val deliveryFee =
            if (items.any { it.itemType == ItemType.CHEKI }) Const.CHEKI_DELIVERY_FEE
            else if (items.any { it.itemType == ItemType.PRINT_GACHA })
                Const.PRINT_GACHA_DELIVERY_FEE
            else null

        return deliveryFee
    }

    private fun isFreeShipping(printItemAmount: Int, chekiItemAmount: Int): Boolean {
        return if (
            shopConfig.featureToggleFreeShipping() &&
                FREE_SHIPPING_MIN_AMOUNT_PHYSICAL_ITEM <= (printItemAmount + chekiItemAmount)
        )
            true
        else if (
            shopConfig.featureTogglePrintGacha() &&
                chekiItemAmount == 0 &&
                FREE_SHIPPING_MIN_AMOUNT_PRINT_ITEM <= printItemAmount
        )
            true
        else false
    }
}
