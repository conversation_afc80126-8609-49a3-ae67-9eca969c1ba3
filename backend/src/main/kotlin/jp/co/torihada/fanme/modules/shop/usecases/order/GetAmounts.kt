package jp.co.torihada.fanme.modules.shop.usecases.order

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.getOrThrow
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.math.BigDecimal
import java.math.RoundingMode
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.controllers.CampaignEntryController
import jp.co.torihada.fanme.modules.shop.Util.Companion.discountOnSale
import jp.co.torihada.fanme.modules.shop.Util.OrderAmounts
import jp.co.torihada.fanme.modules.shop.Util.UnitPrice
import jp.co.torihada.fanme.modules.shop.models.Cart
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.models.ItemType
import jp.co.torihada.fanme.modules.shop.services.DeliveryService

@ApplicationScoped
class GetAmounts : BaseGetAmounts() {

    @Inject private lateinit var campaignEntryController: CampaignEntryController
    @Inject private lateinit var deliveryService: DeliveryService

    data class Input(val cartId: Long, val cartItemIds: List<Long>, val tip: Int)

    fun execute(params: Input): Result<OrderAmounts, FanmeException> {
        val cart = Cart.findById(params.cartId) ?: return Err(ResourceNotFoundException("Cart"))
        val cartItems = cart.items.filter { params.cartItemIds.contains(it.id!!) }

        val isDigital = cartItems.all { it.item.isDigital }

        val unitPrices =
            cartItems
                .map { cartItem ->
                    val sale = cartItem.item.onSale
                    val price =
                        discountOnSale(cartItem.singleFile?.price ?: cartItem.item.price, sale)
                    UnitPrice(cartItem.item, cartItem.singleFile, price, cartItem.quantity)
                }
                .toList()

        val deliveryFee = deliveryService.getCartItemsDeliveryFee(cartItems)
        val itemAmount = unitPrices.sumOf { it.price * it.quantity }

        val fee =
            unitPrices.sumOf {
                val marginRate = getMarginRate(it.item, cart.shop.creatorUid!!)

                calculateMargin(it.price, it.quantity, marginRate, it.item).getOrThrow()
            } +
                BigDecimal(params.tip)
                    .multiply(BigDecimal(cart.shop.tipMarginRate.toString()))
                    .setScale(0, RoundingMode.CEILING)
                    .toInt() +
                // 配送料はテナントの手数料に含める
                (deliveryFee ?: 0)

        val total = itemAmount + params.tip + (deliveryFee ?: 0)
        val profit = total - fee

        return Ok(
            OrderAmounts(
                total = total,
                itemAmount = itemAmount,
                profit = profit,
                fee = fee,
                tip = params.tip,
                unitPrices = unitPrices,
                deliveryFee = deliveryFee,
                isDigital = isDigital,
            )
        )
    }

    private fun getMarginRate(item: Item, userUid: String): BigDecimal {
        // デフォルトはアイテムのマージンレート
        var marginRateString = item.marginRate.toString()
        // キャンペーンマージンレートを適用
        // MarginType1= デジタルバンドルとデジタルファンレターのプラットフォーム手数料が5%になるというキャンペーン
        if (item.itemType == ItemType.DIGITAL_BUNDLE) {
            if (campaignEntryController.hasActiveCampaignEntry(userUid, "MARGIN_TYPE1")) {
                marginRateString = "0.05"
            }
        }

        return BigDecimal(marginRateString)
    }
}
