package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.console.models.ProxyAccessLog

@ApplicationScoped
class CreateProxyAccessLog {

    data class Input(
        val proxyUserUid: String?,
        val targetUserUid: String,
        val method: String,
        val url: String,
        val request: String? = null,
    )

    fun execute(input: Input): Result<Unit, FanmeException> {
        ProxyAccessLog.create(
            proxyUserUid = input.proxyUserUid,
            targetUserUid = input.targetUserUid,
            method = input.method,
            url = input.url,
            request = input.request,
        )

        return Ok(Unit)
    }
}
