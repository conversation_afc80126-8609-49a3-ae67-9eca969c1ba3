package jp.co.torihada.fanme.modules.console.models

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*

@Entity
@Table(name = "proxy_access_logs")
class ProxyAccessLog : BaseModel() {

    @Column(name = "proxy_user_uid", nullable = true) var proxyUserUid: String? = null

    @Column(name = "target_user_uid", nullable = false) var targetUserUid: String = ""

    @Column(nullable = false) var method: String = ""

    @Column(nullable = false, columnDefinition = "TEXT") var url: String = ""

    @Column(columnDefinition = "JSON") var request: String? = null

    companion object : PanacheCompanion<ProxyAccessLog> {
        fun create(
            proxyUserUid: String?,
            targetUserUid: String,
            method: String,
            url: String,
            request: String? = null,
        ): ProxyAccessLog {
            val proxyAccessLog =
                ProxyAccessLog().apply {
                    this.proxyUserUid = proxyUserUid
                    this.targetUserUid = targetUserUid
                    this.method = method
                    this.url = url
                    this.request = request
                }
            proxyAccessLog.persist()
            return proxyAccessLog
        }
    }
}
