package jp.co.torihada.fanme.endpoints.proxy

import io.quarkus.security.identity.AuthenticationRequestContext
import io.quarkus.security.identity.IdentityProvider
import io.quarkus.security.identity.SecurityIdentity
import io.quarkus.security.identity.request.BaseAuthenticationRequest
import io.quarkus.security.runtime.QuarkusSecurityIdentity
import io.smallrye.jwt.auth.principal.JWTParser
import io.smallrye.mutiny.Uni
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject

data class ProxyAuthenticationRequest(val targetUserUid: String, val targetUserFanmeToken: String) :
    BaseAuthenticationRequest()

@ApplicationScoped
class ProxyTokenIdentityProvider : IdentityProvider<ProxyAuthenticationRequest> {
    @Inject lateinit var jwtParser: JWTParser

    override fun getRequestType(): Class<ProxyAuthenticationRequest> {
        return ProxyAuthenticationRequest::class.java
    }

    override fun authenticate(
        request: ProxyAuthenticationRequest,
        context: AuthenticationRequestContext,
    ): Uni<SecurityIdentity> {

        return context.runBlocking {
            val builder = QuarkusSecurityIdentity.builder()

            val bearerToken = request.targetUserFanmeToken.removePrefix("Bearer ")
            val jwt = jwtParser.parse(bearerToken)

            builder.setPrincipal(jwt)

            builder.build()
        }
    }
}
