#
# Common
#
config.tenant=fanme
config.env-kind=test
config.host-url=http://host.docker.internal:27000
config.auth-api-name=fanme.auth.api
config.auth-api-pass=dfgsdjierre11024TDHij)jpjgadfa{-fsefdsgasdfewfkHklfeowabmcfgsStfsf
config.shop-api-name=fanme.shop.api
config.shop-api-pass=huieRE10248{-jdsJDFLewefjsgadfasGfdhjklfjdsfjkl
#
# fanme
#
config.fanme.auth-server-url=http://host.docker.internal:22000
config.fanme.payment-url=http://host.docker.internal:23000
config.fanme.fanme-auth-client-id=test_id_2
config.fanme.fanme-auth-client-secret=test_secret_2
config.fanme.fanme-frontend-url=http://host.docker.internal:11000
config.fanme.s3-bucket-name=fanme-dev-api
config.fanme.s3-endpoint=${quarkus.s3.endpoint-override}
#
# payment
#
# GMO
config.payment.gmo-site-id=tsite00046822
config.payment.gmo-site-pass=m3xvyp6g
config.payment.gmo-shop-id=tshop00054898
config.payment.gmo-shop-pass=ch3q8wfn
config.payment.gmo-product-shop-id=tshop00066824
config.payment.gmo-product-shop-pass=2cauwncy
config.payment.gmo-transfer-shop-id=rshop00000567
config.payment.gmo-transfer-shop-pass=t8k8x3cy
config.payment.gmo-webhook-allowed-remote-ip=***************
#
# shop
#
config.shop.s3-bucket-name=fanme-dev-shop
config.shop.s3-endpoint=${quarkus.s3.endpoint-override}
#
# DB
#
# fanme
quarkus.datasource.fanme.db-kind=h2
quarkus.datasource.fanme.username=sa
quarkus.datasource.fanme.password=
quarkus.datasource.fanme.jdbc.url=jdbc:h2:mem:fanme;MODE=MySQL;DB_CLOSE_DELAY=-1
quarkus.hibernate-orm.fanme.datasource=fanme
quarkus.hibernate-orm.fanme.packages=jp.co.torihada.fanme.modules.fanme.models,jp.co.torihada.fanme.modules.console.models
quarkus.hibernate-orm.fanme.database.generation=drop-and-create
# payment
quarkus.datasource.payment.db-kind=mysql
quarkus.datasource.payment.username=root
quarkus.datasource.payment.password=pass
quarkus.datasource.payment.jdbc.url=***********************************************
quarkus.hibernate-orm.payment.datasource=payment
quarkus.hibernate-orm.payment.packages=jp.co.torihada.fanme.modules.payment.models
# shop
quarkus.datasource.shop.db-kind=mysql
quarkus.datasource.shop.username=root
quarkus.datasource.shop.password=pass
quarkus.datasource.shop.jdbc.url=********************************************
quarkus.hibernate-orm.shop.datasource=shop
quarkus.hibernate-orm.shop.packages=jp.co.torihada.fanme.modules.shop.models
# flyway
quarkus.flyway.fanme.migrate-at-start=false
#quarkus.flyway.payment.migrate-at-start=true
quarkus.flyway.shop.migrate-at-start=true
quarkus.flyway.out-of-order=true
quarkus.flyway.fanme.locations=db/migration/fanme
#quarkus.flyway.payment.locations=db/migration/payment
quarkus.flyway.shop.locations=db/migration/shop
#
# JWT
#
mp.jwt.verify.publickey.location=http://host.docker.internal:22000/oauth2/certs
mp.jwt.verify.issuer=http://host.docker.internal:22000
#
# Log
#
quarkus.log.level=INFO
quarkus.http.access-log.enabled=true
quarkus.log.console.format=%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5p %c{1} - %s%e%n
quarkus.http.access-log.pattern=%s %m - %R %Dms
#
# Client
#
quarkus.rest-client.auth.uri=${config.fanme.auth-server-url}
quarkus.rest-client.shop.uri=${config.host-url}
quarkus.rest-client.payment.uri=${config.fanme.payment-url}
quarkus.rest-client.gmo-mul-pay.uri=https://pt01.mul-pay.jp
quarkus.rest-client.gmo-remittance.uri=https://test-remittance.gmopg.jp
quarkus.rest-client.gmo-transfer.uri=https://test-remittance.gmopg.jp
#
# S3
#
quarkus.s3.path-style-access=true
quarkus.s3.endpoint-override=http://host.docker.internal:23003
quarkus.s3.sync-client.type=url
quarkus.s3.aws.region=ap-northeast-1
quarkus.s3.aws.credentials.type=static
quarkus.s3.aws.credentials.static-provider.access-key-id=test_key
quarkus.s3.aws.credentials.static-provider.secret-access-key=test_key
#
# Mail
#
quarkus.mailer.from=<EMAIL>
quarkus.mailer.host=host.docker.internal
quarkus.mailer.port=22002
quarkus.mailer.username=test
quarkus.mailer.password=test
#
# CORS
#
quarkus.http.cors=true
quarkus.http.cors.origins=*
quarkus.http.cors.methods=GET,PUT,POST,DELETE,OPTIONS
quarkus.http.cors.headers=accept,accept-type,authorization,content-type
quarkus.http.cors.access-control-max-age=600
#
# Feature Toggle
#
config.feature-toggle-cheki=false
config.shop.feature-toggle-print-gacha=false
config.shop.feature-toggle-free-shipping=false
