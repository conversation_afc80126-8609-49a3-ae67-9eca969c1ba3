package jp.co.torihada.fanme.filters

import io.quarkus.test.InjectMock
import io.quarkus.test.junit.QuarkusTest
import io.smallrye.jwt.auth.principal.DefaultJWTCallerPrincipal
import io.smallrye.mutiny.Uni
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.container.ContainerResponseContext
import jakarta.ws.rs.core.MultivaluedHashMap
import jakarta.ws.rs.core.SecurityContext
import jakarta.ws.rs.core.UriInfo
import java.net.URI
import jp.co.torihada.fanme.modules.console.controllers.ProxyAccessLogController
import jp.co.torihada.fanme.modules.console.models.ProxyAccessLog
import jp.co.torihada.fanme.modules.console.services.ProxyAccessTokenService
import jp.co.torihada.fanme.modules.console.usecases.CreateProxyAccessLog
import org.junit.jupiter.api.AfterAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance
import org.mockito.Mockito.*

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ProxyAccessLogFilterTest {

    @Inject lateinit var filter: ProxyAccessLogFilter

    @InjectMock lateinit var proxyTokenService: ProxyAccessTokenService

    @InjectMock lateinit var proxyAccessLogController: ProxyAccessLogController

    private lateinit var mockRequestContext: ContainerRequestContext
    private lateinit var mockResponseContext: ContainerResponseContext
    private lateinit var mockUriInfo: UriInfo
    private lateinit var mockSecurityContext: SecurityContext
    private lateinit var mockPrincipal: DefaultJWTCallerPrincipal

    companion object {
        private const val TEST_URL = "http://localhost:8080/api/test"
        private const val PROXY_TOKEN = "test-token"
        private const val TARGET_TOKEN = "target-token"
        private const val PROXY_USER_UID = "proxy-user-uid-1"
        private const val TARGET_USER_UID = "target-user-uid-2"
        private const val REGULAR_USER_UID = "user-uuid-200"
        private const val METHOD_PUT = "PUT"
        private const val STATUS_OK = 200
        private const val WAIT_TIME = 100L
    }

    @BeforeEach
    fun setup() {
        mockRequestContext = mock(ContainerRequestContext::class.java)
        mockResponseContext = mock(ContainerResponseContext::class.java)
        mockUriInfo = mock(UriInfo::class.java)
        mockSecurityContext = mock(SecurityContext::class.java)
        mockPrincipal = mock(DefaultJWTCallerPrincipal::class.java)

        `when`(mockRequestContext.uriInfo).thenReturn(mockUriInfo)
        `when`(mockUriInfo.absolutePath).thenReturn(URI.create(TEST_URL))
        `when`(mockResponseContext.status).thenReturn(STATUS_OK)
    }

    @AfterEach
    fun cleanup() {
        reset(
            mockRequestContext,
            mockResponseContext,
            mockUriInfo,
            proxyTokenService,
            proxyAccessLogController,
        )
    }

    @AfterAll
    @Transactional
    fun cleanupDatabase() {
        ProxyAccessLog.deleteAll()
    }

    @Test
    @DisplayName("プロキシトークンを使用したPUTリクエストでアクセスログが記録される")
    fun shouldLogProxyAccessForSuccessfulPUTRequest() {
        val requestBody = """{"data": "update-data"}"""
        setupProxyTokenRequest(requestBody)

        filter.filter(mockRequestContext, mockResponseContext)
        Thread.sleep(WAIT_TIME)

        verify(proxyAccessLogController)
            .createProxyAccessLog(
                CreateProxyAccessLog.Input(
                    proxyUserUid = PROXY_USER_UID,
                    targetUserUid = TARGET_USER_UID,
                    method = METHOD_PUT,
                    url = TEST_URL,
                    request = requestBody,
                )
            )
    }

    @Test
    @DisplayName("JWT認証された通常ユーザーのPUTリクエストでアクセスログが記録される")
    fun shouldLogRegularAccessForAuthenticatedUserWithPUTRequest() {
        val requestBody = """{"data": "put-data"}"""
        setupJWTAuthenticatedRequest(requestBody)

        filter.filter(mockRequestContext, mockResponseContext)
        Thread.sleep(WAIT_TIME)

        verify(proxyAccessLogController)
            .createProxyAccessLog(
                CreateProxyAccessLog.Input(
                    proxyUserUid = null,
                    targetUserUid = REGULAR_USER_UID,
                    method = METHOD_PUT,
                    url = TEST_URL,
                    request = requestBody,
                )
            )
    }

    private fun setupCommonRequest(
        requestBody: String,
        method: String = METHOD_PUT,
        headers: MultivaluedHashMap<String, String>,
    ) {
        `when`(mockRequestContext.headers).thenReturn(headers)
        `when`(mockRequestContext.method).thenReturn(method)
        `when`(mockRequestContext.getProperty("proxy_access_request_body")).thenReturn(requestBody)
    }

    private fun setupProxyTokenRequest(requestBody: String) {
        val headers = MultivaluedHashMap<String, String>()
        headers.add("X-Proxy-Access-Token", PROXY_TOKEN)
        setupCommonRequest(requestBody, METHOD_PUT, headers)

        val proxyData =
            ProxyAccessTokenService.ProxyAccessData(
                targetUserFanmeToken = TARGET_TOKEN,
                proxyUserUid = PROXY_USER_UID,
                targetUserUid = TARGET_USER_UID,
            )
        `when`(proxyTokenService.retrieveProxyAccessData(PROXY_TOKEN))
            .thenReturn(Uni.createFrom().item(proxyData))
    }

    private fun setupJWTAuthenticatedRequest(requestBody: String) {
        val headers = MultivaluedHashMap<String, String>()
        setupCommonRequest(requestBody, METHOD_PUT, headers)

        `when`(mockPrincipal.subject).thenReturn(REGULAR_USER_UID)
        `when`(mockRequestContext.securityContext).thenReturn(mockSecurityContext)
        `when`(mockSecurityContext.userPrincipal).thenReturn(mockPrincipal)
    }
}
