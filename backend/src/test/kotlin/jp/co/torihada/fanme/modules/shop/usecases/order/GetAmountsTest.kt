package jp.co.torihada.fanme.modules.shop.usecases.order

import com.github.michaelbull.result.unwrap
import io.quarkus.test.InjectMock
import io.quarkus.test.TestTransaction
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.controllers.CampaignEntryController
import jp.co.torihada.fanme.modules.shop.Config as ShopConfig
import jp.co.torihada.fanme.modules.shop.Const.DEFAULT_CHEKI_MARGIN_RATE
import jp.co.torihada.fanme.modules.shop.factories.ItemFactory
import jp.co.torihada.fanme.modules.shop.factories.ShopFactory
import jp.co.torihada.fanme.modules.shop.models.Cart
import jp.co.torihada.fanme.modules.shop.models.CartItem
import jp.co.torihada.fanme.modules.shop.models.ItemType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito

@QuarkusTest
class GetAmountsTest {
    @InjectMock lateinit var campaignEntryController: CampaignEntryController
    @Inject lateinit var getAmounts: GetAmounts
    @Inject private lateinit var shopConfig: ShopConfig

    @BeforeEach
    fun setUp() {
        Mockito.`when`(
                campaignEntryController.hasActiveCampaignEntry(
                    userUid = "test-uid",
                    actionType = "MARGIN_TYPE1",
                )
            )
            .thenReturn(true)
    }

    @Test
    @TestTransaction
    fun `test get amounts with digital bundle`() {
        val userId = "test-user-uid"
        val shop = ShopFactory.new()
        shop.persistAndFlush()

        val digitalBundleItem =
            ItemFactory.new(
                shopId = shop.id!!,
                name = "デジタルバンドル",
                itemType = ItemType.DIGITAL_BUNDLE,
                price = 1000,
            )
        digitalBundleItem.persistAndFlush()

        val cart = Cart.create(userId, shop.id!!)

        val digitalBundleCartItem =
            CartItem.create(
                cartId = cart.id!!,
                itemId = digitalBundleItem.id!!,
                quantity = 1,
                singleFileId = null,
            )
        digitalBundleCartItem.persistAndFlush()

        cart.items.add(digitalBundleCartItem)
        cart.persistAndFlush()

        val input =
            GetAmounts.Input(
                cartId = cart.id!!,
                cartItemIds = listOf(digitalBundleCartItem.id!!),
                tip = 500,
            )

        val result = getAmounts.execute(input).unwrap()

        assertEquals(1500, result.total)
        assertEquals(1000, result.itemAmount)
        assertEquals(500, result.tip)
        assertEquals(1401, result.profit)
        assertEquals(99, result.fee)
        assertEquals(null, result.deliveryFee)
    }

    @Test
    @TestTransaction
    fun `test get amounts with cheki`() {
        val userId = "test-user-uid"

        val shop = ShopFactory.new()
        shop.persistAndFlush()

        val chekiItem =
            ItemFactory.new(
                shopId = shop.id!!,
                name = "チェキ",
                itemType = ItemType.CHEKI,
                price = 1500,
                marginRate = DEFAULT_CHEKI_MARGIN_RATE,
            )
        chekiItem.persistAndFlush()

        val cart = Cart.create(userId, shop.id!!)

        val chekiCartItem =
            CartItem.create(
                cartId = cart.id!!,
                itemId = chekiItem.id!!,
                quantity = 2,
                singleFileId = null,
            )
        chekiCartItem.persistAndFlush()

        cart.items.add(chekiCartItem)
        cart.persistAndFlush()

        val input =
            GetAmounts.Input(
                cartId = cart.id!!,
                cartItemIds = listOf(chekiCartItem.id!!),
                tip = 500,
            )

        val result = getAmounts.execute(input).unwrap()

        assertEquals(3900, result.total)
        assertEquals(3000, result.itemAmount)
        assertEquals(500, result.tip)
        assertEquals(2313, result.profit)
        assertEquals(1587, result.fee)
        assertEquals(400, result.deliveryFee)
    }

    @Test
    @TestTransaction
    fun `test get amounts with acrylic stand`() {
        val userId = "test-user-uid"

        val shop = ShopFactory.new()
        shop.persistAndFlush()

        val acrylicStandItem =
            ItemFactory.new(
                shopId = shop.id!!,
                name = "【アクスタ枠付】アクリルスタンド",
                itemType = ItemType.CHEKI,
                price = 2000,
                marginRate = DEFAULT_CHEKI_MARGIN_RATE,
            )
        acrylicStandItem.persistAndFlush()

        val cart = Cart.create(userId, shop.id!!)

        val acrylicStandCartItem =
            CartItem.create(
                cartId = cart.id!!,
                itemId = acrylicStandItem.id!!,
                quantity = 1,
                singleFileId = null,
            )
        acrylicStandCartItem.persistAndFlush()

        cart.items.add(acrylicStandCartItem)
        cart.persistAndFlush()

        val input =
            GetAmounts.Input(
                cartId = cart.id!!,
                cartItemIds = listOf(acrylicStandCartItem.id!!),
                tip = 500,
            )

        val result = getAmounts.execute(input).unwrap()

        assertEquals(2900, result.total)
        assertEquals(2000, result.itemAmount)
        assertEquals(500, result.tip)
        assertEquals(1245, result.profit)
        assertEquals(1655, result.fee)
        assertEquals(400, result.deliveryFee)
    }

    @Test
    @TestTransaction
    fun `test get amounts with many cheki items`() {
        val userId = "test-user-uid"

        val shop = ShopFactory.new()
        shop.persistAndFlush()

        val chekiItem1 =
            ItemFactory.new(
                shopId = shop.id!!,
                name = "チェキ",
                itemType = ItemType.CHEKI,
                price = 1500,
                marginRate = DEFAULT_CHEKI_MARGIN_RATE,
            )
        chekiItem1.persistAndFlush()

        val chekiItem2 =
            ItemFactory.new(
                shopId = shop.id!!,
                name = "チェキ",
                itemType = ItemType.CHEKI,
                price = 2500,
                marginRate = DEFAULT_CHEKI_MARGIN_RATE,
            )
        chekiItem2.persistAndFlush()

        val cart = Cart.create(userId, shop.id!!)

        val chekiCartItem1 =
            CartItem.create(
                cartId = cart.id!!,
                itemId = chekiItem1.id!!,
                quantity = 2,
                singleFileId = null,
            )
        chekiCartItem1.persistAndFlush()
        cart.items.add(chekiCartItem1)

        val chekiCartItem2 =
            CartItem.create(
                cartId = cart.id!!,
                itemId = chekiItem2.id!!,
                quantity = 1,
                singleFileId = null,
            )
        chekiCartItem2.persistAndFlush()
        cart.items.add(chekiCartItem2)

        cart.persistAndFlush()

        val input =
            GetAmounts.Input(
                cartId = cart.id!!,
                cartItemIds = listOf(chekiCartItem1.id!!, chekiCartItem2.id!!),
                tip = 500,
            )

        val result = getAmounts.execute(input).unwrap()

        if (shopConfig.featureToggleFreeShipping()) {
            assertEquals(6000, result.total)
            assertEquals(5500, result.itemAmount)
            assertEquals(500, result.tip)
            assertEquals(3944, result.profit)
            assertEquals(2056, result.fee)
            assertEquals(0, result.deliveryFee)
        } else {
            assertEquals(6400, result.total)
            assertEquals(5500, result.itemAmount)
            assertEquals(500, result.tip)
            assertEquals(3944, result.profit)
            assertEquals(2456, result.fee)
            assertEquals(400, result.deliveryFee)
        }
    }

    @Test
    @TestTransaction
    fun `test get amounts with many cheki and digital item`() {
        val userId = "test-user-uid"

        val shop = ShopFactory.new()
        shop.persistAndFlush()

        val chekiItem1 =
            ItemFactory.new(
                shopId = shop.id!!,
                name = "チェキ",
                itemType = ItemType.CHEKI,
                price = 1500,
                marginRate = DEFAULT_CHEKI_MARGIN_RATE,
            )
        chekiItem1.persistAndFlush()

        val digitalBundleItem =
            ItemFactory.new(
                shopId = shop.id!!,
                name = "デジタルバンドル",
                itemType = ItemType.DIGITAL_BUNDLE,
                price = 1000,
            )
        digitalBundleItem.persistAndFlush()

        val cart = Cart.create(userId, shop.id!!)

        val chekiCartItem1 =
            CartItem.create(
                cartId = cart.id!!,
                itemId = chekiItem1.id!!,
                quantity = 2,
                singleFileId = null,
            )
        chekiCartItem1.persistAndFlush()
        cart.items.add(chekiCartItem1)

        val digitalCartItem =
            CartItem.create(
                cartId = cart.id!!,
                itemId = digitalBundleItem.id!!,
                quantity = 1,
                singleFileId = null,
            )
        digitalCartItem.persistAndFlush()
        cart.items.add(digitalCartItem)

        cart.persistAndFlush()

        val input =
            GetAmounts.Input(
                cartId = cart.id!!,
                cartItemIds = listOf(chekiCartItem1.id!!, digitalCartItem.id!!),
                tip = 500,
            )

        val result = getAmounts.execute(input).unwrap()

        assertEquals(4900, result.total)
        assertEquals(4000, result.itemAmount)
        assertEquals(500, result.tip)
        assertEquals(3263, result.profit)
        assertEquals(1637, result.fee)
        assertEquals(400, result.deliveryFee)
    }

    @Test
    @TestTransaction
    fun `test get amounts with merchandise`() {
        val userId = "test-user-uid"

        val shop = ShopFactory.new()
        shop.persistAndFlush()

        val merchandiseItem =
            ItemFactory.new(
                shopId = shop.id!!,
                name = "【物販】オリジナルTシャツ",
                itemType = ItemType.CHEKI,
                price = 2000,
                marginRate = DEFAULT_CHEKI_MARGIN_RATE,
            )
        merchandiseItem.persistAndFlush()

        val cart = Cart.create(userId, shop.id!!)

        val merchandiseCartItem =
            CartItem.create(
                cartId = cart.id!!,
                itemId = merchandiseItem.id!!,
                quantity = 1,
                singleFileId = null,
            )
        merchandiseCartItem.persistAndFlush()

        cart.items.add(merchandiseCartItem)
        cart.persistAndFlush()

        val input =
            GetAmounts.Input(
                cartId = cart.id!!,
                cartItemIds = listOf(merchandiseCartItem.id!!),
                tip = 500,
            )

        val result = getAmounts.execute(input).unwrap()

        assertEquals(2900, result.total)
        assertEquals(2000, result.itemAmount)
        assertEquals(500, result.tip)
        assertEquals(1851, result.profit)
        assertEquals(1049, result.fee)
        assertEquals(400, result.deliveryFee)
    }
}
