'use client';
import React from 'react';
import clsx from 'clsx';
import ShopPublicImage from '@/components/ShopImage';

type SectionTitleWithIconProps = {
  title: string;
  icon: string;
  className?: string;
  required?: boolean;
};

const SectionTitleWithIcon = ({ title, icon, className, required }: SectionTitleWithIconProps) => {
  return (
    <h4 className={clsx('flex items-center justify-start gap-1.5 text-medium-15', className)}>
      <ShopPublicImage src={icon} alt="icon" width={20} height={20} />
      {title}
      {required && <span className="text-regular-11 text-orange-300">*必須</span>}
    </h4>
  );
};

export default SectionTitleWithIcon;
