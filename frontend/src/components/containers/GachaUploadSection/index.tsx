'use client';
import React, { useRef, useMemo } from 'react';
import { useParams } from 'next/navigation';
import type { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { useExhibitsStore } from '@/store/useExhibit';
import GachaDuplicateSettings from './gacha-duplicate-settings';
import GachaProbabilitySettings from './gacha-probability-settings';
import GachaBackDesignUpload from './gacha-back-design-upload';
import GachaTotalCount from './gacha-total-count';
import GachaUploadItems from './gacha-upload-items';
import type { ExhibitGachaItem } from '@/types/exhibitItem';
import { ITEM_TYPE } from '@/types/item';
import { ExhibitType } from '@/types/shopItem';

type GachaUploadSectionProps = {
  numbering: string;
  shopLimitation: ShopLimitation;
  isEdit?: boolean;
  itemType?: ExhibitType;
};

const GachaUploadSection = ({ numbering, shopLimitation, isEdit, itemType }: GachaUploadSectionProps) => {
  // 合計ガチャ数セクションの参照を作成
  const totalGachaCountSectionRef = useRef<HTMLDivElement>(null);

  const params = useParams();
  const itemId = params.id as string;
  const { exhibits, setIsDuplicated } = useExhibitsStore();

  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]) as ExhibitGachaItem;

  const isDuplicated = exhibitItem?.isDuplicated ?? true;
  const isPrintGacha = itemType === ITEM_TYPE.PRINT_GACHA.str;

  const hasItems = exhibitItem?.itemFiles && exhibitItem.itemFiles.length > 0;
  const tempItemFiles = exhibitItem?.itemFiles || [];
  const { hasEnoughItems = false } = exhibitItem || {};

  const onChangeDuplicate = (value: boolean) => {
    setIsDuplicated(itemId, value);
  };

  return (
    <div>
      <div className="section-double-border">
        <GachaUploadItems
          numbering={numbering}
          shopLimitation={shopLimitation}
          totalGachaCountSectionRef={totalGachaCountSectionRef}
          isEdit={isEdit}
        />
        {hasItems && (
          <>
            <GachaTotalCount
              itemFiles={tempItemFiles}
              hasEnoughItems={hasEnoughItems}
              totalGachaCountSectionRef={totalGachaCountSectionRef}
            />
            {!isPrintGacha && (
              <GachaDuplicateSettings
                isDuplicated={isDuplicated}
                onChangeDuplicate={onChangeDuplicate}
                disabled={isEdit}
              />
            )}
            <GachaProbabilitySettings isEdit={isEdit} isDuplicated={isDuplicated} />
            <GachaBackDesignUpload shopLimitation={shopLimitation} isEdit={isEdit} />
          </>
        )}
      </div>
    </div>
  );
};

export default GachaUploadSection;
