'use client';
import React from 'react';
import { useParams } from 'next/navigation';
import SectionInstruction from '@/components/atoms/sectionInstruction';
import SectionTitleWithIcon from '@/components/atoms/typography/section-title-with-icon';
import UploadedFile from '@/components/containers/UploadedFile';
import UploadFile from '@/components/containers/UploadFile';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { useExhibitsStore } from '@/store/useExhibit';
import { useUploadProgress } from '@/hooks/useUploadProgress';
import type { ExhibitGachaItem } from '@/types/exhibitItem';

type GachaBackDesignUploadProps = {
  shopLimitation: ShopLimitation;
  isEdit?: boolean;
};

const GachaBackDesignUpload = ({ shopLimitation, isEdit }: GachaBackDesignUploadProps) => {
  const params = useParams();
  const itemId = params.id as string;
  const { exhibits, setBackDesignFile } = useExhibitsStore();
  const { uploadProgress, handleProgress, resetProgress } = useUploadProgress();

  const exhibitItem = exhibits.find((e) => e.itemId === itemId) as ExhibitGachaItem;
  const tempItemFiles = exhibitItem?.itemFiles || [];
  const backDesignFile = exhibitItem?.backDesignFile;

  const handleDelete = () => {
    setBackDesignFile(itemId, null);
    resetProgress();
  };

  return (
    // アイテムがない場合は何も表示しない
    tempItemFiles.length === 0 ? null : (
      <section className="px-4 pb-4">
        <div className="mb-4 flex items-center gap-2">
          <SectionTitleWithIcon title="背面デザインアップロード" icon="/images/gacha/icons/Gacha.svg" required />
        </div>

        <SectionInstruction
          title={`トレカの裏面に印刷する\nデータをアップロードしてください。\n背面デザインは1種類、固定となります`}
          className="mb-4"
        />

        <div className="flex justify-start">
          {backDesignFile ? (
            <UploadedFile
              id={backDesignFile.id}
              src={backDesignFile.type === 'image' ? backDesignFile.thumbnail || backDesignFile.src : ''}
              type={backDesignFile.type}
              title={backDesignFile.title}
              isLoading={backDesignFile.isLoading || false}
              progress={uploadProgress[backDesignFile.id] || 0}
              setThumbImage={() => {}}
              handleDelete={!isEdit ? handleDelete : undefined}
              showType={false}
              showTitle={false}
              uploadType="back-design"
            />
          ) : (
            <UploadFile
              itemFiles={[]}
              setFiles={(files) => {
                if (files.length > 0) {
                  setBackDesignFile(itemId, files[0]);
                }
              }}
              shopLimitation={shopLimitation}
              isPublic={false}
              onProgress={handleProgress}
              multiple={false}
              maxUpload={1}
            />
          )}
        </div>
      </section>
    )
  );
};

export default GachaBackDesignUpload;
