'use client';

import React, { useEffect, useMemo, useRef } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import CustomToast from '@/components/atoms/toast/custom-toast';
import SectionTitle from '@/components/atoms/typography/section-title';
import DigitalGachaItem from '@/components/containers/digital-gacha-item';
import { useGetItem, useGetDeliveryFee } from '@/lib/client-api/item-endpoint/item-endpoint';
import { useGetTipLimit } from '@/lib/client-api/order-endpoint/order-endpoint';
import { FeatureFlags } from '@/lib/feature';
import { useOrderFormStore } from '@/store/useOrderFormStore';
import OrderPageTemplate from './_components/templates/order-page-template';
import AddressSection from '@/app/[identityId]/order/address-section';
import {
  DIGITAL_GACHA_MULTI_PULL_COUNT,
  DIGITAL_GACHA_SINGLE_PULL_COUNT,
  PRINT_GACHA_MIN_PULL_COUNT,
  PRINT_GACHA_MAX_PULL_COUNT,
} from '@/consts/gacha-data';
import { FREE_SHIPPING_MIN_AMOUNT_PRINT_ITEM } from '@/consts/order';
import { STATUS_ERROR } from '@/consts/status';
import { useGetConvenienceFees } from '@/hooks/swr/useGetConvenienceFees';
import { useCheckPullableGachaStatus } from '@/hooks/use-check-pullable-gacha-status';
import { useGachaOrderProcess } from '@/hooks/use-gacha-order-process';
import { useShopCreatorEventInfo } from '@/hooks/use-shop-creator-event-info';
import { getUserIdentityId } from '@/utils/base';
import { calculateFee } from '@/utils/cart';
import { ITEM_TYPE, getItemTypeValueFromStr, type ItemTypeString, type ItemTypeValue } from '@/types/item';
import { createGachaOrderFormSchema, OrderFormData } from '@/types/order';
import type { AxiosError } from 'axios';

const DUMMY_ITEM_TYPE_FOR_SWR = 0 as ItemTypeValue;

const GachaOrderPage = () => {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();

  const { tip, paymentMethod, convenienceParam, selectedCard, clearOtherPaymentParams, setConvenienceResult } =
    useOrderFormStore();

  const identityId = getUserIdentityId(params.identityId as string);
  const itemId = searchParams.get('itemId') || '';
  const quantity = Number(searchParams.get('quantity')) || 1;
  const itemType = searchParams.get('itemType') || '';

  // TODO: orvalが生成したSWRの戻り値のerrorに、デフォルトで型が付くようにする
  const { data: itemData, error } = useGetItem<AxiosError>(identityId, Number(itemId));
  const { data: tipLimitData } = useGetTipLimit(identityId);
  const { data: convenienceFeesData } = useGetConvenienceFees();

  const item = itemData?.data?.item;
  const tipLimit = tipLimitData?.data?.tipLimit?.amount;
  const convenienceFees = convenienceFeesData?.data || [];

  const isPrintGacha = itemType === ITEM_TYPE.PRINT_GACHA.str;
  const shouldFetchDeliveryFee = FeatureFlags.printGacha() && isPrintGacha;

  const itemTypeValue = shouldFetchDeliveryFee
    ? getItemTypeValueFromStr(itemType as ItemTypeString)
    : DUMMY_ITEM_TYPE_FOR_SWR;

  const { data: deliveryFeeData } = useGetDeliveryFee(identityId, itemTypeValue, {
    swr: {
      enabled: shouldFetchDeliveryFee,
    },
  });

  const deliveryFee = shouldFetchDeliveryFee ? deliveryFeeData?.data?.deliveryFee || 0 : 0;

  const itemTotalPrice = (item?.currentPrice ?? item?.price ?? 0) * quantity;
  const { yellCount, boostRatio, hasShopCreatorActiveEvent } = useShopCreatorEventInfo(identityId, itemTotalPrice);
  const fee = calculateFee(paymentMethod, itemTotalPrice, tip, convenienceFees);
  const totalAmount = itemTotalPrice + tip + fee + deliveryFee;

  const { checkPullableGachaStatus } = useCheckPullableGachaStatus(Number(itemId), identityId);
  const gachaOrderSchema = createGachaOrderFormSchema();

  const {
    formState: { isValid },
    trigger,
    setValue,
  } = useForm<OrderFormData>({
    resolver: zodResolver(gachaOrderSchema),
    defaultValues: {
      paymentMethod: paymentMethod as OrderFormData['paymentMethod'],
      tip: tip,
      selectedCard: selectedCard,
      convenienceParam: convenienceParam,
    },
    mode: 'onChange',
  });

  const hasCheckedPullableGachaStatus = useRef(false);

  // 支払い方法変更時に再バリデーション
  useEffect(() => {
    setValue('paymentMethod', paymentMethod as OrderFormData['paymentMethod']);
    setValue('selectedCard', selectedCard);
    trigger();
  }, [paymentMethod, selectedCard, setValue, trigger]);

  const { processPayment, isProcessing } = useGachaOrderProcess({
    payment: { paymentMethod, selectedCard, convenienceParam },
    item: { itemId: Number(itemId), item: item || null },
    amount: { tip, totalAmount },
    identityId,
    quantity,
    callbacks: { clearOtherPaymentParams, setConvenienceResult, checkPullableGachaStatus },
    trigger,
  });

  useEffect(() => {
    if (itemId && identityId && item && !hasCheckedPullableGachaStatus.current) {
      checkPullableGachaStatus(`/${identityId}`);
      hasCheckedPullableGachaStatus.current = true;
    }
  }, [itemId, identityId, item, checkPullableGachaStatus]);

  useEffect(() => {
    if (error?.status === 404) {
      router.push(`/${identityId}`);
      return;
    }
  }, [error, identityId, router]);

  useEffect(() => {
    if (searchParams.get('status') === STATUS_ERROR) {
      toast.custom((t) => CustomToast(t, STATUS_ERROR, '決済に失敗しました'));
    }
  }, [searchParams]);

  const isValidQuantity = useMemo(() => {
    if (!item) return false;

    if (FeatureFlags.printGacha() && itemType === ITEM_TYPE.PRINT_GACHA.str) {
      const itemTypeValue = getItemTypeValueFromStr(itemType as ItemTypeString);
      if (itemTypeValue === ITEM_TYPE.PRINT_GACHA.value) {
        return quantity >= PRINT_GACHA_MIN_PULL_COUNT && quantity <= PRINT_GACHA_MAX_PULL_COUNT;
      }
    }

    const { isDuplicatedDigitalGachaItems, remainingUniquePullCount } = item;
    const validQuantities = [DIGITAL_GACHA_SINGLE_PULL_COUNT, DIGITAL_GACHA_MULTI_PULL_COUNT];
    if (!isDuplicatedDigitalGachaItems && typeof remainingUniquePullCount === 'number') {
      validQuantities.push(remainingUniquePullCount);
    }
    return validQuantities.includes(quantity);
  }, [item, quantity, itemType]);

  useEffect(() => {
    if (item && !isValidQuantity) {
      router.replace(`/${identityId}/item/${itemId}`);
    }
  }, [isValidQuantity, item, router, identityId, itemId]);

  if (!item) return <></>;

  return (
    <OrderPageTemplate
      tipLimitAmount={tipLimit || 0}
      cartItemsTotalPrice={itemTotalPrice}
      fee={fee}
      tip={tip}
      deliveryFee={isPrintGacha ? (itemTotalPrice < FREE_SHIPPING_MIN_AMOUNT_PRINT_ITEM ? deliveryFee : 0) : undefined}
      yellCount={yellCount}
      boostRatio={boostRatio}
      hasShopCreatorActiveEvent={hasShopCreatorActiveEvent}
      identityId={identityId}
      isValid={isValid && !isProcessing && isValidQuantity}
      onProcessPayment={processPayment}
    >
      {isPrintGacha && (
        <section className="section-double-border mb-4 w-full px-4 pb-4">
          <SectionTitle title="配送先" className="mb-4" />
          <AddressSection />
        </section>
      )}
      <section className="w-full px-4 pb-4">
        <SectionTitle title="商品" className="mb-4" />
        <DigitalGachaItem item={item} quantity={quantity} />
      </section>
    </OrderPageTemplate>
  );
};

export default GachaOrderPage;
