import { ITEM_TYPE } from './item';
import { AwardProbability, GachaBenefit, GachaItemFile } from '@/types/gacha';
import { Benefit, Discount, ExhibitType, ItemFiles, Period, SingleItem } from '@/types/shopItem';
import type { LeastItemCount } from '@/consts/sizes';

type ItemTypeValue = (typeof ITEM_TYPE)[keyof typeof ITEM_TYPE]['value'];
// 単一の出品アイテムの型定義
export interface BaseExhibitItem {
  itemId: string;
  thumbnail: string;
  thumbnailCustomImage: string;
  thumbnailType: 'custom' | 'upload';
  thumbnailBlurLevel: number;
  thumbnailWatermarkLevel: number;
  thumbnailRatio: number;
  title: string;
  description?: string;
  tags?: string[];
  priceSet: number;
  limited?: number;
  remainingAmount?: number;
  period?: Period;
  discount?: Discount;
  password?: string;
  available: boolean;
  isLoading?: boolean;
  itemType: ItemTypeValue;
  isPublishLocked?: boolean;
}
export interface DigitalBundle extends BaseExhibitItem {
  singleSale: boolean;
  singleSaleDefault: boolean;
  singlePrice: number;
  selectedIndex: number | null;
  itemFiles: ItemFiles;
  benefits?: Benefit[];
  samples?: SingleItem[];
}

// FIXME ExhibitGachaItemをExhibitDigitalGachaItemに変更する
// 現在、デジタルガチャがExhibitGachaItem、プリントガチャがExhibitPrintGachaItemを使っている。
// 命名からデジタルガチャのみが使っているとわかるようにするためにrenameする
export interface ExhibitGachaItem extends Omit<BaseExhibitItem, 'password'> {
  totalCapacity: number;
  isDuplicated: boolean;
  awardProbabilities: AwardProbability[];
  itemFiles: GachaItemFile[];
  benefits?: GachaBenefit[];
  samples?: GachaItemFile[];
  isAwardProbabilitiesValid: boolean;
  hasEnoughItems: boolean;
  backDesignFile?: SingleItem | null;
}

export interface ExhibitPrintGachaItem extends ExhibitGachaItem {
  isDuplicated: false;
}

export interface ExhibitChekiItem extends BaseExhibitItem {
  limitedPerUser: number;
  benefits?: Benefit[];
  samples?: SingleItem[];
  itemFiles?: undefined;
}

// ストアのインターフェース
export interface ExhibitsStore {
  exhibits: (DigitalBundle | ExhibitGachaItem | ExhibitChekiItem)[];
  marginRate: number;
  isConfirmed: boolean;
  setIsConfirmed: (isConfirmed: boolean) => void;
  isNew: (itemId: string) => boolean;
  setSelectedIndex: (itemId: string, index: number | null) => void;
  setMarginRate: (rate: number) => void;
  addExhibit: (itemId: string, itemType: ExhibitType) => void;
  removeExhibit: (itemId: string) => void;
  setItemFiles: (fileId: string, files: ItemFiles) => void;
  setItemType: (itemId: string, itemType: ItemTypeValue) => void;
  setThumbnail: (itemId: string, thumbnail: string) => void;
  setThumbnailCustomImage: (itemId: string, thumbnail: string) => void;
  setThumbnailType: (itemId: string, type: 'custom' | 'upload') => void;
  setThumbnailBlurLevel: (itemId: string, level: number) => void;
  setThumbnailWatermarkLevel: (itemId: string, watermark: number) => void;
  setThumbnailRatio: (itemId: string, ratio: number) => void;
  setSamples: (itemId: string, samples: SingleItem[] | GachaItemFile[]) => void;
  setTitle: (itemId: string, title: string) => void;
  setDescription: (itemId: string, description: string) => void;
  setTags: (itemId: string, tags: string[]) => void;
  setPriceSet: (itemId: string, price: number) => void;
  setSingleSale: (itemId: string, singleSale: boolean) => void;
  setSingleSaleDefault: (itemId: string, singleSale: boolean) => void;
  setSinglePrice: (itemId: string, price: number) => void;
  setLimited: (itemId: string, limited: number) => void;
  setLimitedPerUser: (itemId: string, limited: number) => void;
  setBenefits: (itemId: string, benefits: Benefit[] | GachaBenefit[]) => void;
  setPeriod: (itemId: string, period: Period) => void;
  setDiscount: (itemId: string, discount: { percentage: number; start?: Date | string; end?: Date | string }) => void;
  setPassword: (itemId: string, password: string) => void;
  setAvailable: (itemId: string, available: boolean) => void;
  setAwardProbability: (itemId: string, probability: AwardProbability[]) => void;
  setIsAwardProbabilitiesValid: (itemId: string, isAwardProbabilitiesValid: boolean) => void;
  setHasEnoughItems: (itemId: string, activeAwardCount: LeastItemCount) => void;
  setIsDuplicated: (itemId: string, isDuplicated: boolean) => void;
  setTotalCapacity: (itemId: string, totalCapacity: number) => void;
  setBackDesignFile: (itemId: string, backDesignFile: SingleItem | null) => void;
  setIsPublishLocked: (itemId: string, isPublishLocked: boolean) => void;
  reset: (itemId: string) => void;
  validateExhibitItem: (itemId: string) => boolean;
  validateItemFiles: (itemId: string) => boolean;
  validateTitle: (itemId: string) => boolean;
  validateDescription: (itemId: string) => boolean;
  validatePriceSet: (itemId: string) => boolean;
  validateThumbnail: (itemId: string) => boolean;
  validateSamples: (itemId: string) => boolean;
  validateBenefits: (itemId: string) => boolean;
  validateSingleSale: (itemId: string) => boolean;
  validateSinglePriceAll: (itemId: string) => boolean;
  validateSinglePrice: (itemId: string, file: SingleItem) => boolean;
  validateLimited: (itemId: string) => boolean;
  validateLimitedPerUser: (itemId: string) => boolean;
  validatePeriodStart: (itemId: string) => boolean;
  validatePeriodEnd: (itemId: string) => boolean;
  validateDiscount: (itemId: string) => boolean;
  validateDiscountStart: (itemId: string) => boolean;
  validateDiscountEnd: (itemId: string) => boolean;
  validatePassword: (itemId: string) => boolean;
  validateGachaUploadData: (itemId: string) => boolean;
}
