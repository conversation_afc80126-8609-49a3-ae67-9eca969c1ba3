/**
 * 出品状態を管理するカスタムフック
 */

import { create } from 'zustand';
import { persist, PersistOptions } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { PROBABILITY_PATTERNS, GACHA_PATTERN_KEYS } from '@/consts/gacha-data';
import {
  ITEM_MAX_PRICE,
  ITEM_MAX_QUANTITY_PER_USER,
  ITEM_MAX_SALES_COUNT,
  ITEM_MIN_PRICE,
  ITEM_MIN_PRICE_FOR_CHEKI,
  ITEM_PASSWORD_MAX_LENGTH,
  ITEM_PASSWORD_MIN_LENGTH,
} from '@/consts/inputLength';
import { LEAST_ITEM_COUNT } from '@/consts/sizes';
import {
  isDigitalBundle,
  isDigitalItem,
  isPhysicalItem,
  isPrintGacha,
  isExhibitCheki,
  isGacha,
} from '@/utils/itemTypes';
import { DigitalBundle, ExhibitsStore, ExhibitGachaItem, ExhibitChekiItem } from '@/types/exhibitItem';
import { Award, CONDITION_TYPE } from '@/types/gacha';
import { ITEM_TYPE } from '@/types/item';
import { ExhibitType, SingleItem } from '@/types/shopItem';

// ベースとなる共通の出品アイテムプロパティ
const baseExhibitProperties = {
  thumbnail: '',
  thumbnailCustomImage: '',
  thumbnailType: 'custom' as const,
  thumbnailBlurLevel: 1,
  thumbnailWatermarkLevel: 1,
  thumbnailRatio: 1,
  samples: [],
  title: '',
  tags: [],
  priceSet: 0,
  limited: 0,
  period: {
    start: undefined as Date | undefined,
    end: undefined as Date | undefined,
  },
  discount: {
    percentage: 0,
    start: undefined as Date | undefined,
    end: undefined as Date | undefined,
  },
  password: '',
  available: true,
  isLoading: false,
  description: '',
};

// デジタルバンドル用の空の出品アイテム
const emptyExhibit: Omit<DigitalBundle, 'itemId'> = {
  ...baseExhibitProperties,
  itemFiles: [],
  selectedIndex: null,
  singleSale: false,
  singleSaleDefault: false,
  singlePrice: 0,
  itemType: ITEM_TYPE.DIGITAL_BUNDLE.value,
  benefits: [
    {
      benefitFiles: [],
      description: '',
      conditionType: 0,
    },
  ],
};

// チェキ用の空の出品アイテム
const emptyExhibitCheki: Omit<ExhibitChekiItem, 'itemId'> = {
  ...baseExhibitProperties,
  limitedPerUser: 1,
  itemType: ITEM_TYPE.CHEKI.value,
  benefits: [
    {
      benefitFiles: [],
      description: '',
      conditionType: 0,
    },
  ],
};

// ガチャ用の空の出品アイテム
const emptyExhibitGacha: Omit<ExhibitGachaItem, 'itemId'> = {
  ...baseExhibitProperties,
  itemType: ITEM_TYPE.DIGITAL_GACHA.value,
  itemFiles: [], // GachaItemFile[] 型
  samples: [], // GachaItemFile[] 型 (base と同じだが明示)
  awardProbabilities: Object.entries(PROBABILITY_PATTERNS[GACHA_PATTERN_KEYS.PATTERN_1]).map(
    ([awardType, probability]) => ({
      awardType: Number(awardType) as Award,
      probability,
    }),
  ),
  benefits: [
    {
      files: [],
      benefitFiles: [],
      description: '',
      conditionType: CONDITION_TYPE.TIMES_10,
    },
  ],
  isAwardProbabilitiesValid: false,
  hasEnoughItems: false,
  isDuplicated: true,
  totalCapacity: 0,
  backDesignFile: null,
};

// 永続化の設定
const persistOptions: PersistOptions<ExhibitsStore, Pick<ExhibitsStore, 'exhibits'>> = {
  name: 'exhibits-storage',
  partialize: (state) => ({
    exhibits: state.exhibits,
  }),
  storage: {
    getItem: (name) => {
      if (typeof window === 'undefined') return null;
      try {
        const str = localStorage.getItem(name);
        return str ? JSON.parse(str) : null;
      } catch (err) {
        console.error('Error reading from localStorage:', err);
        return null;
      }
    },
    setItem: (name, value) => {
      if (typeof window === 'undefined') return;
      try {
        localStorage.setItem(name, JSON.stringify(value));
      } catch (err) {
        console.error('Error writing to localStorage:', err);
      }
    },
    removeItem: (name) => {
      if (typeof window === 'undefined') return;
      try {
        localStorage.removeItem(name);
      } catch (err) {
        console.error('Error removing from localStorage:', err);
      }
    },
  },
};

// Zustandストアの作成
export const useExhibitsStore = create<ExhibitsStore>()(
  persist(
    immer<ExhibitsStore>((set, get) => ({
      exhibits: [],
      isConfirmed: false,
      setIsConfirmed: (isConfirmed) =>
        set((state) => {
          state.isConfirmed = isConfirmed;
        }),
      marginRate: 0,
      isNew: (itemId: string) => itemId.startsWith('new'),
      setMarginRate: (rate) =>
        set((state) => {
          state.marginRate = rate;
        }),
      setItemType: (itemId, itemType) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.itemType = itemType;
            state.exhibits = [...state.exhibits];
          }
        }),
      setItemFiles: (fileId, itemFiles) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === fileId);
          if (exhibit && (isDigitalItem(exhibit) || isPrintGacha(exhibit))) {
            exhibit.itemFiles = Array.isArray(itemFiles) ? [...itemFiles] : itemFiles;
            state.exhibits = [...state.exhibits];
          }
        }),
      setSelectedIndex: (itemId, index) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId) as DigitalBundle;
          if (exhibit) {
            exhibit.selectedIndex = index;
            state.exhibits = [...state.exhibits];
          }
        }),
      setThumbnail: (itemId, thumbnail) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.thumbnail = thumbnail;
            state.exhibits = [...state.exhibits];
          }
        }),
      setThumbnailCustomImage: (itemId, thumbnail) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.thumbnailCustomImage = thumbnail;
            state.exhibits = [...state.exhibits];
          }
        }),

      setThumbnailType: (itemId, type) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.thumbnailType = type;
            state.exhibits = [...state.exhibits];
          }
        }),

      setThumbnailBlurLevel: (itemId, level) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.thumbnailBlurLevel = level;
            state.exhibits = [...state.exhibits];
          }
        }),

      setThumbnailWatermarkLevel: (itemId, level) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.thumbnailWatermarkLevel = level;
            state.exhibits = [...state.exhibits];
          }
        }),

      setThumbnailRatio: (itemId, ratio) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.thumbnailRatio = ratio;
            state.exhibits = [...state.exhibits];
          }
        }),

      setSamples: (itemId, samples) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.samples = samples;
            state.exhibits = [...state.exhibits];
          }
        }),

      setTitle: (itemId, title) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.title = title;
            state.exhibits = [...state.exhibits];
          }
        }),

      setDescription: (itemId, description) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.description = description;
            state.exhibits = [...state.exhibits];
          }
        }),

      setTags: (itemId, tags) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.tags = tags;
            state.exhibits = [...state.exhibits];
          }
        }),

      setPriceSet: (itemId, price) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.priceSet = price;
            state.exhibits = [...state.exhibits];
          }
        }),

      setSingleSale: (itemId, singleSale) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            (exhibit as DigitalBundle).singleSale = singleSale;
            state.exhibits = [...state.exhibits];
          }
        }),
      setSingleSaleDefault: (itemId, singleSale) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            (exhibit as DigitalBundle).singleSaleDefault = singleSale;
            state.exhibits = [...state.exhibits];
          }
        }),

      setSinglePrice: (itemId, price) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId) as DigitalBundle;
          if (exhibit) {
            exhibit.singlePrice = price;
            state.exhibits = [...state.exhibits];
          }
        }),

      setLimited: (itemId, limited) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.limited = limited;
            state.exhibits = [...state.exhibits];
          }
        }),

      setLimitedPerUser: (itemId, limitedPerUser) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId) as ExhibitChekiItem;
          if (exhibit) {
            exhibit.limitedPerUser = limitedPerUser;
            state.exhibits = [...state.exhibits];
          }
        }),

      setBenefits: (itemId, benefits) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.benefits = benefits;
            state.exhibits = [...state.exhibits];
          }
        }),

      setPeriod: (itemId, period) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.period = period;
            state.exhibits = [...state.exhibits];
          }
        }),

      setDiscount: (itemId, discount) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.discount = discount;
            state.exhibits = [...state.exhibits];
          }
        }),

      setPassword: (itemId, password) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (isGacha(exhibit)) return;
          if (exhibit) {
            exhibit.password = password;
            state.exhibits = [...state.exhibits];
          }
        }),

      setAvailable: (itemId, available) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.available = available;
            state.exhibits = [...state.exhibits];
          }
        }),

      setAwardProbability: (itemId, probability) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            (exhibit as ExhibitGachaItem).awardProbabilities = probability;
            state.exhibits = [...state.exhibits];
          }
        }),
      setIsAwardProbabilitiesValid: (itemId, isAwardProbabilitiesValid) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            (exhibit as ExhibitGachaItem).isAwardProbabilitiesValid = isAwardProbabilitiesValid;
            state.exhibits = [...state.exhibits];
          }
        }),
      setHasEnoughItems: (itemId, activeAwardCount) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            const gachaItems = (exhibit as ExhibitGachaItem).itemFiles;
            (exhibit as ExhibitGachaItem).hasEnoughItems = gachaItems.length >= LEAST_ITEM_COUNT[activeAwardCount];
            state.exhibits = [...state.exhibits];
          }
        }),
      setIsDuplicated: (itemId, isDuplicated) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            (exhibit as ExhibitGachaItem).isDuplicated = isDuplicated;
            state.exhibits = [...state.exhibits];
          }
        }),
      setTotalCapacity: (itemId, totalCapacity) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            (exhibit as ExhibitGachaItem).totalCapacity = totalCapacity;
            state.exhibits = [...state.exhibits];
          }
        }),
      setBackDesignFile: (itemId, backDesignFile) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            (exhibit as ExhibitGachaItem).backDesignFile = backDesignFile;
            state.exhibits = [...state.exhibits];
          }
        }),
      setIsPublishLocked: (itemId, isPublishLocked) =>
        set((state) => {
          const exhibit = state.exhibits.find((e) => e.itemId === itemId);
          if (exhibit) {
            exhibit.isPublishLocked = isPublishLocked;
            state.exhibits = [...state.exhibits];
          }
        }),

      // その他のメソッド
      addExhibit: (itemId: any, itemType: ExhibitType) =>
        set((state) => {
          if (!state.exhibits.some((e) => e.itemId === itemId)) {
            if (itemType === ITEM_TYPE.DIGITAL_GACHA.str) {
              state.exhibits.push({ ...emptyExhibitGacha, itemId });
            } else if (itemType === ITEM_TYPE.PRINT_GACHA.str) {
              state.exhibits.push({
                ...emptyExhibitGacha,
                itemId,
                isDuplicated: true,
                itemType: ITEM_TYPE.PRINT_GACHA.value,
              });
            } else if (itemType === ITEM_TYPE.CHEKI.str) {
              state.exhibits.push({ ...emptyExhibitCheki, itemId });
            } else {
              state.exhibits.push({ ...emptyExhibit, itemId });
            }
          }
        }),

      removeExhibit: (itemId) =>
        set((state) => {
          state.exhibits = state.exhibits.filter((e) => e.itemId !== itemId);
        }),

      reset: (itemId) =>
        set((state) => {
          state.exhibits = state.exhibits.filter((e) => e.itemId !== itemId);
        }),

      validateExhibitItem: (itemId) => {
        return (
          get().validateItemFiles(itemId) &&
          get().validateTitle(itemId) &&
          get().validateDescription(itemId) &&
          get().validatePriceSet(itemId) &&
          get().validateThumbnail(itemId) &&
          get().validateSamples(itemId) &&
          get().validateBenefits(itemId) &&
          get().validateSingleSale(itemId) &&
          get().validateSinglePriceAll(itemId) &&
          get().validateLimited(itemId) &&
          get().validateLimitedPerUser(itemId) &&
          get().validatePeriodStart(itemId) &&
          get().validatePeriodEnd(itemId) &&
          get().validateDiscount(itemId) &&
          get().validateDiscountStart(itemId) &&
          get().validateDiscountEnd(itemId) &&
          get().validatePassword(itemId) &&
          get().validateGachaUploadData(itemId)
        );
      },

      validateItemFiles: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        // デジタル商品の場合、1つ以上の商品ファイルがアップロードされていること
        return !!exhibit && (isPhysicalItem(exhibit) || exhibit.itemFiles.length > 0);
      },
      validateTitle: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        return !!exhibit && (exhibit.title || '').trim().length > 0; // 入力必須
      },
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      validateDescription: (itemId) => {
        return true; // 任意
      },
      validatePriceSet: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        // 価格が設定されていること、上限下限以内で設定されていること
        const minPrice = isPhysicalItem(exhibit) ? ITEM_MIN_PRICE_FOR_CHEKI : ITEM_MIN_PRICE;
        return !!exhibit && exhibit.priceSet > 0 && exhibit.priceSet <= ITEM_MAX_PRICE && exhibit.priceSet >= minPrice;
      },
      validateThumbnail: (itemId): boolean => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId) as DigitalBundle | ExhibitGachaItem;
        if (!exhibit || exhibit.thumbnail.length === 0) return false;
        // ExhibitGachaItem の場合、thumbnailType は常に 'custom'
        if (isGacha(exhibit)) {
          return exhibit.thumbnailType === 'custom';
        } else {
          // これは DigitalBundle
          return (
            exhibit.thumbnailType === 'custom' ||
            (exhibit.thumbnailType === 'upload' && exhibit.selectedIndex !== undefined)
          );
        }
      },
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      validateSamples: (itemId) => {
        return true; // 任意
      },
      validateBenefits: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        if (!exhibit || !exhibit.benefits) return true;

        // 特典が1個のみの場合は画像未設定でもエラーにしない
        if (exhibit.benefits.length <= 1) {
          return true;
        }

        // 特典が2個以上の場合は全ての特典で画像設定を必須とする
        return exhibit.benefits.every((benefit) => benefit.benefitFiles && benefit.benefitFiles.length > 0);
      },
      validateSingleSale: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId) as DigitalBundle;
        if (!exhibit || !isDigitalBundle(exhibit)) return true;
        if (exhibit.singleSale === false) return true; // 単品で売らない場合はOK
        // 単品の設定がある場合は、1つ以上のファイルが単品販売に設定されていること
        const singleSeleCount = exhibit.itemFiles.filter((file) => (file as SingleItem).isSingleSale === true).length;
        if (singleSeleCount === 0) return false;
        return true;
      },
      validateSinglePriceAll: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId) as DigitalBundle;
        if (!exhibit || !isDigitalBundle(exhibit)) return true;
        if (exhibit.singleSale === false) return true; // 単品で売らない場合はOK
        // 全てのファイルを個別にチェック
        return !!exhibit && exhibit.itemFiles.every((file) => get().validateSinglePrice(itemId, file));
      },
      validateSinglePrice: (itemId, file) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId) as DigitalBundle;
        if (!exhibit || !isDigitalBundle(exhibit)) return true;
        const itemFile = exhibit.itemFiles.find((f) => f === file) as SingleItem;
        if (!itemFile) return true;
        if (itemFile.isSingleSale === false) return true; // 売らない場合はOK
        const singlePrice = itemFile.price || 0;
        // 単品の設定がある場合は、上限下限以内で設定されていること
        const maxPrice = exhibit.priceSet > 0 ? exhibit.priceSet : ITEM_MAX_PRICE;
        return ITEM_MIN_PRICE <= singlePrice && singlePrice <= maxPrice;
      },
      validateLimited: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        if (!exhibit) return true;
        if (!exhibit.limited) return true; // 設定がない場合はOK
        // 販売数が設定されている場合は、上限以内で設定
        return 1 <= exhibit.limited && exhibit.limited <= ITEM_MAX_SALES_COUNT;
      },
      validateLimitedPerUser: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId) as ExhibitChekiItem;
        if (!exhibit) return true;
        if (!isExhibitCheki(exhibit)) return true; // 設定がない場合はOK、chekiの場合はスキップしない

        // 動的な最大値を計算（limitedが設定されている場合はそれを上限とする）
        const maxLimitedPerUser =
          exhibit.limited && exhibit.limited < ITEM_MAX_QUANTITY_PER_USER
            ? exhibit.limited
            : ITEM_MAX_QUANTITY_PER_USER;

        return 1 <= exhibit.limitedPerUser && exhibit.limitedPerUser <= maxLimitedPerUser;
      },
      validatePeriodStart: (itemId) => {
        // DateInputコンポーネント上のチェックはコンポーネント内のmin,maxでチェックされているので注意
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        if (!exhibit) return true;
        const isCheki = isExhibitCheki(exhibit);
        if (!isCheki && !exhibit.period?.start && !exhibit.period?.end) return true; // 両方設定がない場合はOK、chekiの場合はスキップしない
        // chekiの場合は、開始日が設定されていること
        if (isCheki && !exhibit.period?.start) return false;
        // (新規のみ)今より未来を設定が必要
        const now = new Date(new Date().setSeconds(0, 0));
        return !(get().isNew(itemId) && !!exhibit.period?.start && new Date(exhibit.period?.start) < now);
      },
      validatePeriodEnd: (itemId) => {
        // DateInputコンポーネント上のチェックはコンポーネント内のmin,maxでチェックされているので注意
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        if (!exhibit) return true;
        const isCheki = isExhibitCheki(exhibit);
        if (!isCheki && !exhibit.period?.start && !exhibit.period?.end) return true; // 両方設定がない場合はOK、chekiの場合はスキップしない
        // chekiの場合は、終了日が設定されていること
        if (isCheki && !exhibit.period?.end) return false;
        // 開始日と終了日の両方が入っている場合は、逆転していないことをチェック
        if (
          exhibit.period?.start &&
          exhibit.period?.end &&
          new Date(exhibit.period.end) <= new Date(exhibit.period.start)
        ) {
          return false;
        }
        // 開始日（または今日）より未来を設定が必要
        const now = new Date(new Date().setSeconds(0, 0));
        const checkDate = exhibit.period?.start ? new Date(exhibit.period?.start) : now;
        return !(exhibit.period?.end && new Date(exhibit.period?.end) <= checkDate);
      },
      validateDiscount: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        if (!exhibit) return true;
        const percentage = exhibit.discount?.percentage || 0;
        if (percentage === 0) return true; // 割引の設定がない場合はOK
        // 割引の設定がない場合はOK
        if (percentage < 1 || 99 < percentage) return false;
        // 割引率が1%以上99%以下で、割引後の価格が最低価格未満であること
        const discountedPrice = exhibit.priceSet - (exhibit.priceSet * percentage) / 100;
        if (discountedPrice < ITEM_MIN_PRICE) return false;
        return true; // 任意設定
      },
      validateDiscountStart: (itemId) => {
        // DateInputコンポーネント上のチェックはコンポーネント内のmin,maxでチェックされているので注意
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        if (!exhibit) return true;
        if (exhibit && (exhibit.discount?.percentage || 0) === 0) return true; // 割引の設定がない場合はOK
        // 現在時刻よりも未来、または販売期間が設定されて
        if (exhibit.discount?.start === undefined && exhibit.discount?.end === undefined) return false;
        return true;
      },
      validateDiscountEnd: (itemId) => {
        // DateInputコンポーネント上のチェックはコンポーネント内のmin,maxでチェックされているので注意
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        if (!exhibit) return true;
        if (exhibit && (exhibit.discount?.percentage || 0) === 0) return true; // 割引の設定がない場合はOK
        // 終了日は設定必須
        if (!exhibit.discount?.end) return false;
        // 開始日と終了日の両方が入っている場合は、逆転していないことをチェック
        if (
          exhibit.discount?.start &&
          exhibit.discount?.end &&
          new Date(exhibit.discount.end) <= new Date(exhibit.discount.start)
        ) {
          return false;
        }
        return true;
      },
      validatePassword: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        if (!exhibit) return true;
        if (isGacha(exhibit)) return true;
        if (!exhibit.password) return true; // 設定がない場合はOK
        // パスワードが設定されている場合は、上限下限以内で設定されていること
        return (
          ITEM_PASSWORD_MIN_LENGTH <= exhibit.password.length && exhibit.password.length <= ITEM_PASSWORD_MAX_LENGTH
        );
      },
      validateGachaUploadData: (itemId) => {
        const exhibit = get().exhibits.find((e) => e.itemId === itemId);
        if (!exhibit) return true;
        const isGachaItem = isGacha(exhibit);
        if (!isGachaItem) return true;
        const gachaItem = exhibit as ExhibitGachaItem;
        const { isDuplicated, isAwardProbabilitiesValid, hasEnoughItems } = gachaItem;
        if (!hasEnoughItems) return false;
        if (!isDuplicated) return true;
        return isAwardProbabilitiesValid;
      },
    })),
    persistOptions,
  ),
);
