{"name": "frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build-dev1": "NEXT_PUBLIC_APP_ENV=dev1 APP_ENV=dev1 next build", "build-dev2": "NEXT_PUBLIC_APP_ENV=dev2 APP_ENV=dev2 next build", "build-demo": "NEXT_PUBLIC_APP_ENV=demo APP_ENV=demo next build", "build-staging": "NEXT_PUBLIC_APP_ENV=staging APP_ENV=staging next build", "build-production": "NEXT_PUBLIC_APP_ENV=production APP_ENV=production next build", "start": "next start", "lint": "eslint \"{src,config}/**/*.{ts,tsx,js,jsx}\" --quiet", "lint-fix": "eslint \"{src,config}/**/*.{ts,tsx,js,jsx}\" --fix --quiet", "lint-staged": "lint-staged", "test:types": "tsc --noEmit", "test": "jest", "orval-generate": "yarn orval && yarn transform:api-schemas && yarn lint-fix", "transform:api-schemas": "yarn transform:snake-to-camel src/lib/server-api && yarn transform:snake-to-camel src/lib/client-api && yarn transform:snake-to-camel src/lib/fanme-api", "transform:snake-to-camel": "tsx src/scripts/snake-to-camel.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^3.9.1", "@kayahr/stackblur": "^2.0.1", "@next/third-parties": "^15.1.4", "@tailwindcss/typography": "^0.5.16", "@types/cleave.js": "^1.4.12", "axios": "^1.7.7", "axios-retry": "^4.5.0", "cleave.js": "^1.6.0", "clsx": "^2.1.1", "compressorjs": "^1.2.1", "cookie": "^1.0.2", "fs": "^0.0.1-security", "gsap": "^3.12.5", "http-errors": "^2.0.0", "https": "^1.0.0", "immer": "^10.1.1", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "next": "14.2.25", "query-string": "^9.1.0", "react": "^18", "react-dom": "^18", "react-easy-crop": "^5.1.0", "react-hook-form": "^7.53.1", "react-hot-toast": "^2.4.1", "react-loading-skeleton": "^3.5.0", "react-markdown": "^10.1.0", "swiper": "^11.1.14", "swr": "^2.2.5", "tailwind-merge": "^2.5.2", "tsx": "^4.19.3", "yet-another-react-lightbox": "^3.21.7", "zod": "^3.25.20", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@svgr/webpack": "^8.1.0", "@types/http-errors": "^2.0.4", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.25", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.30.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-tailwindcss": "^3.18.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.3.0", "msw": "^2.7.3", "orval": "7.9.0", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "^3.4.1", "ts-jest": "^29.2.6", "ts-node": "^10.9.2", "typescript": "^5"}, "lint-staged": {"src/**/*.{ts,tsx,js,jsx}": "eslint"}, "engines": {"node": ">=20.17.0"}, "resolutions": {"string-width": "4.2.3", "strip-ansi": "6.0.1", "wrap-ansi": "7.0.0", "ansi-regex": "5.0.1", "cliui": "7.0.4"}, "msw": {"workerDirectory": ["public"]}}