{"parser": "@typescript-eslint/parser", "parserOptions": {"project": ["./tsconfig.json"], "tsconfigRootDir": "./", "sourceType": "module"}, "ignorePatterns": ["next.config.js"], "extends": ["next/core-web-vitals", "next/typescript", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended", "plugin:tailwindcss/recommended", "eslint-config-prettier"], "plugins": ["prettier", "import", "tailwindcss"], "settings": {"import/resolver": {"typescript": {"project": "./tsconfig.json"}}}, "rules": {"react-hooks/exhaustive-deps": "error", "@typescript-eslint/no-explicit-any": ["off", {}], "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-var-requires": "off", "no-console": ["warn", {"allow": ["warn", "error"]}], "no-debugger": "warn", "import/order": ["error", {"groups": ["builtin", "external", "parent", "sibling", "index", "internal", "object", "type"], "pathGroups": [{"pattern": "react*", "group": "builtin", "position": "before"}, {"pattern": "@/components/**", "group": "parent", "position": "before"}, {"pattern": "@/lib/**", "group": "parent", "position": "after"}, {"pattern": "@/actions/**", "group": "parent", "position": "after"}, {"pattern": "@/store/**", "group": "parent", "position": "after"}, {"pattern": "@/types/**", "group": "type", "position": "before"}], "pathGroupsExcludedImportTypes": ["react"], "newlines-between": "never", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "tailwindcss/classnames-order": "warn", "tailwindcss/no-custom-classname": ["error", {"whitelist": ["quantity-slider", "triangle-right-white"]}], "tailwindcss/no-contradicting-classname": "error", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}]}}